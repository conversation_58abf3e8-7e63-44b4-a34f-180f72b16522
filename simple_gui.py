import tkinter as tk
from tkinter import ttk, messagebox
import hashlib
import time
import random

class LinkGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("链接生成器")
        self.root.geometry("250x250")
        self.root.resizable(True, False)
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # URL输入框
        self.url_entry = tk.Entry(main_frame, width=70, font=("Arial", 10))
        self.url_entry.pack(pady=(0, 15))

        # 设置默认URL
        default_url = "https://app.lifepointspanel.com/registration?affiliate_id=1583&affiliate_name=Spark+Social+Media&offer_id=1194&offer_name=SPARKFB_PTJ_DOI_UK_EN_NULL&referer=lifepointspanel"
        self.url_entry.insert(0, default_url)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(0, 15))

        # 生成按钮
        self.generate_btn = ttk.Button(button_frame, text="生成", command=self.generate_link)
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 复制按钮
        self.copy_btn = ttk.Button(button_frame, text="复制", command=self.copy_link, state="disabled")
        self.copy_btn.pack(side=tk.LEFT)

        # Transaction ID显示框
        self.transaction_id_entry = tk.Entry(main_frame, width=70, font=("Arial", 10), state="readonly")
        self.transaction_id_entry.pack(pady=(0, 10))

        # 结果显示框
        self.result_entry = tk.Entry(main_frame, width=70, font=("Arial", 10), state="readonly")
        self.result_entry.pack()
        
    def generate_transaction_id(self):
        """生成交易ID"""
        seed = f"{time.time()}_{random.randint(1000000, 9999999)}"
        hash_value = hashlib.md5(seed.encode()).hexdigest()
        transaction_id = "102" + hash_value[:23]
        return transaction_id
    
    def generate_link(self):
        """生成链接"""
        try:
            basic_url = self.url_entry.get().strip()

            if not basic_url:
                messagebox.showerror("错误", "请输入URL")
                return

            transaction_id = self.generate_transaction_id()

            if "?" in basic_url:
                generated_link = f"{basic_url}&transaction_id={transaction_id}"
            else:
                generated_link = f"{basic_url}?transaction_id={transaction_id}"

            # 显示transaction_id
            self.transaction_id_entry.config(state="normal")
            self.transaction_id_entry.delete(0, tk.END)
            self.transaction_id_entry.insert(0, transaction_id)
            self.transaction_id_entry.config(state="readonly")

            # 显示完整链接
            self.result_entry.config(state="normal")
            self.result_entry.delete(0, tk.END)
            self.result_entry.insert(0, generated_link)
            self.result_entry.config(state="readonly")

            # 启用复制按钮并改变文字提示生成成功
            self.copy_btn.config(state="normal")
            self.generate_btn.config(text="生成成功")

            # 2秒后恢复按钮文字
            self.root.after(2000, lambda: self.generate_btn.config(text="生成"))

        except Exception as e:
            messagebox.showerror("错误", f"生成链接时发生错误: {str(e)}")
    
    def copy_link(self):
        """复制链接到剪贴板"""
        try:
            link = self.result_entry.get()
            if link:
                self.root.clipboard_clear()
                self.root.clipboard_append(link)
                self.root.update()
            else:
                messagebox.showwarning("警告", "没有可复制的链接")
        except Exception as e:
            messagebox.showerror("错误", f"复制链接时发生错误: {str(e)}")

def main():
    root = tk.Tk()
    app = LinkGeneratorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
