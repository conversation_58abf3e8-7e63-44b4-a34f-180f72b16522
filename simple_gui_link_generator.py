import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import hashlib
import time
import random

class LinkGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("链接生成器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="链接生成器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Basic URL 输入区域
        ttk.Label(main_frame, text="Basic URL:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        self.url_entry = tk.Text(main_frame, height=4, width=60, wrap=tk.WORD, font=("Arial", 9))
        self.url_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 设置默认URL
        default_url = "https://app.lifepointspanel.com/en/registration?affiliate_id=1583&affiliate_name=Spark+Social+Media&offer_id=1194&offer_name=SPARKFB_PTJ_DOI_UK_EN_NULL&referer=lifepointspanel"
        self.url_entry.insert("1.0", default_url)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20))
        
        # 生成按钮
        self.generate_btn = ttk.Button(button_frame, text="生成链接", command=self.generate_link)
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空按钮
        self.clear_btn = ttk.Button(button_frame, text="清空", command=self.clear_all)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 复制按钮（使用系统剪贴板）
        self.copy_btn = ttk.Button(button_frame, text="复制链接", command=self.copy_link, state="disabled")
        self.copy_btn.pack(side=tk.LEFT)
        
        # 生成的链接显示区域
        ttk.Label(main_frame, text="生成的链接:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        
        # 使用ScrolledText来显示生成的链接
        self.result_text = scrolledtext.ScrolledText(main_frame, height=6, width=60, wrap=tk.WORD, font=("Arial", 9))
        self.result_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        self.result_text.config(state=tk.DISABLED)
        
        # Transaction ID 显示
        ttk.Label(main_frame, text="Transaction ID:", font=("Arial", 10, "bold")).grid(row=6, column=0, sticky=tk.W, pady=(0, 5))
        
        self.transaction_id_var = tk.StringVar()
        self.transaction_id_entry = ttk.Entry(main_frame, textvariable=self.transaction_id_var, state="readonly", font=("Arial", 9))
        self.transaction_id_entry.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def generate_transaction_id(self):
        """生成交易ID"""
        # 创建一个随机种子
        seed = f"{time.time()}_{random.randint(1000000, 9999999)}"
        
        # 生成哈希值
        hash_value = hashlib.md5(seed.encode()).hexdigest()
        
        # 添加前缀并截取适当长度
        transaction_id = "102" + hash_value[:23]  # 总长度为26
        
        return transaction_id
    
    def generate_link(self):
        """生成链接"""
        try:
            # 获取用户输入的URL
            basic_url = self.url_entry.get("1.0", tk.END).strip()
            
            if not basic_url:
                messagebox.showerror("错误", "请输入Basic URL")
                return
            
            # 生成交易ID
            transaction_id = self.generate_transaction_id()
            
            # 检查URL是否已经包含参数
            if "?" in basic_url:
                # 如果已经有参数，使用&连接
                generated_link = f"{basic_url}&transaction_id={transaction_id}"
            else:
                # 如果没有参数，使用?开始
                generated_link = f"{basic_url}?transaction_id={transaction_id}"
            
            # 显示结果
            self.result_text.config(state=tk.NORMAL)
            self.result_text.delete("1.0", tk.END)
            self.result_text.insert("1.0", generated_link)
            self.result_text.config(state=tk.DISABLED)
            
            # 显示Transaction ID
            self.transaction_id_var.set(transaction_id)
            
            # 启用复制按钮
            self.copy_btn.config(state="normal")
            
            # 更新状态
            self.status_var.set("链接生成成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成链接时发生错误: {str(e)}")
            self.status_var.set("生成失败")
    
    def copy_link(self):
        """复制链接到剪贴板（使用tkinter内置功能）"""
        try:
            link = self.result_text.get("1.0", tk.END).strip()
            if link:
                # 使用tkinter的剪贴板功能
                self.root.clipboard_clear()
                self.root.clipboard_append(link)
                self.root.update()  # 确保剪贴板更新
                self.status_var.set("链接已复制到剪贴板")
                messagebox.showinfo("成功", "链接已复制到剪贴板")
            else:
                messagebox.showwarning("警告", "没有可复制的链接")
        except Exception as e:
            messagebox.showerror("错误", f"复制链接时发生错误: {str(e)}")
    
    def clear_all(self):
        """清空所有内容"""
        self.url_entry.delete("1.0", tk.END)
        self.result_text.config(state=tk.NORMAL)
        self.result_text.delete("1.0", tk.END)
        self.result_text.config(state=tk.DISABLED)
        self.transaction_id_var.set("")
        self.copy_btn.config(state="disabled")
        self.status_var.set("已清空")

def main():
    root = tk.Tk()
    app = LinkGeneratorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
