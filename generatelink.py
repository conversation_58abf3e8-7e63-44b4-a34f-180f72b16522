import hashlib
import time
import random

def generate_transaction_id():
    # 创建一个随机种子
    seed = f"{time.time()}_{random.randint(1000000, 9999999)}"
    
    # 生成哈希值
    hash_value = hashlib.md5(seed.encode()).hexdigest()
    
    # 添加前缀并截取适当长度
    transaction_id = "102" + hash_value[:23]  # 总长度为26
    
    return transaction_id

def generate_link():
    # 生成交易ID
    transaction_id = generate_transaction_id()
    # 基础URL（移除了原有的transaction_id参数）
    basic_url = "https://app.lifepointspanel.com/en/registration?affiliate_id=1583&affiliate_name=Spark+Social+Media&offer_id=1194&offer_name=SPARKFB_PTJ_DOI_UK_EN_NULL&referer=lifepointspanel"
    # 正确添加transaction_id参数
    link = f"{basic_url}&transaction_id={transaction_id}"
    return link

# 这些代码应该在函数外部
link = generate_link()
print(link)